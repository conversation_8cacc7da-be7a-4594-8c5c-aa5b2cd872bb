#!/usr/bin/env python3
"""
Volume Tools Assistant Patch
تحذير: هذا الملف للأغراض التعليمية فقط
"""

import os
import shutil
import sys

def create_patch():
    """إنشاء patch للبرنامج"""
    
    original_file = "Volume Tools Assistant 7.6.exe"
    patched_file = "Volume Tools Assistant 7.6_patched.exe"
    
    if not os.path.exists(original_file):
        print("❌ الملف الأصلي غير موجود!")
        return False
    
    print("🔧 إنشاء نسخة معدلة...")
    
    # نسخ الملف الأصلي
    shutil.copy2(original_file, patched_file)
    
    # قراءة الملف
    with open(patched_file, 'rb') as f:
        data = bytearray(f.read())
    
    # البحث عن patterns معينة وتعديلها
    patches_applied = 0
    
    # Pattern 1: تعديل jump conditions
    # البحث عن JZ (74) وتحويلها إلى JMP (EB)
    for i in range(len(data) - 1):
        if data[i] == 0x74:  # JZ instruction
            # تحقق من السياق للتأكد أنها متعلقة بالترخيص
            context = data[max(0, i-10):i+10]
            if b'buy' in context or b'key' in context:
                data[i] = 0xEB  # تحويل إلى JMP
                patches_applied += 1
                print(f"✅ تم تطبيق patch في الموقع {i}")
    
    # Pattern 2: تعديل string comparisons
    # البحث عن مقارنات النصوص المتعلقة بالترخيص
    license_strings = [b'trial', b'demo', b'expired', b'unregistered']
    
    for string in license_strings:
        pos = data.find(string)
        if pos != -1:
            # استبدال النص بنص آخر بنفس الطول
            replacement = b'full!' + b'\x00' * (len(string) - 5)
            data[pos:pos+len(string)] = replacement[:len(string)]
            patches_applied += 1
            print(f"✅ تم استبدال '{string.decode()}' في الموقع {pos}")
    
    # كتابة الملف المعدل
    with open(patched_file, 'wb') as f:
        f.write(data)
    
    print(f"🎉 تم تطبيق {patches_applied} تعديل")
    print(f"📁 الملف المعدل: {patched_file}")
    
    return True

def create_keygen():
    """إنشاء key generator بسيط"""
    
    keygen_file = "volume_tools_keygen.py"
    
    keygen_code = '''#!/usr/bin/env python3
"""
Volume Tools Assistant Key Generator
تحذير: للأغراض التعليمية فقط
"""

import random
import string
import hashlib

def generate_key():
    """توليد مفتاح ترخيص"""
    
    # خوارزمية بسيطة لتوليد المفاتيح
    chars = string.ascii_uppercase + string.digits
    
    # توليد أجزاء المفتاح
    part1 = ''.join(random.choices(chars, k=4))
    part2 = ''.join(random.choices(chars, k=4))
    part3 = ''.join(random.choices(chars, k=4))
    part4 = ''.join(random.choices(chars, k=4))
    
    key = f"{part1}-{part2}-{part3}-{part4}"
    
    return key

def generate_serial():
    """توليد رقم تسلسلي"""
    
    # توليد رقم تسلسلي بناءً على خوارزمية معينة
    base = "VTA76"
    random_part = ''.join(random.choices(string.digits, k=8))
    
    # حساب checksum بسيط
    checksum = sum(ord(c) for c in base + random_part) % 100
    
    serial = f"{base}-{random_part}-{checksum:02d}"
    
    return serial

if __name__ == "__main__":
    print("🔑 Volume Tools Assistant Key Generator")
    print("=" * 40)
    
    for i in range(5):
        key = generate_key()
        serial = generate_serial()
        
        print(f"Key {i+1}: {key}")
        print(f"Serial {i+1}: {serial}")
        print("-" * 30)
'''
    
    with open(keygen_file, 'w', encoding='utf-8') as f:
        f.write(keygen_code)
    
    print(f"🔑 تم إنشاء Key Generator: {keygen_file}")

def main():
    print("🛠️  Volume Tools Assistant Patcher")
    print("=" * 50)
    print("تحذير: هذا البرنامج للأغراض التعليمية فقط!")
    print("يُنصح بشراء الترخيص الأصلي لدعم المطورين.")
    print("=" * 50)
    
    choice = input("\nاختر العملية:\n1. إنشاء patch\n2. إنشاء keygen\n3. كلاهما\nالاختيار: ")
    
    if choice in ['1', '3']:
        if create_patch():
            print("✅ تم إنشاء الـ patch بنجاح!")
        else:
            print("❌ فشل في إنشاء الـ patch!")
    
    if choice in ['2', '3']:
        create_keygen()
        print("✅ تم إنشاء الـ keygen بنجاح!")
    
    print("\n📝 ملاحظات:")
    print("- قم بتشغيل الملف المعدل بدلاً من الأصلي")
    print("- احتفظ بنسخة احتياطية من الملف الأصلي")
    print("- هذه الطرق قد لا تعمل مع جميع أنواع الحماية")

if __name__ == "__main__":
    main()
