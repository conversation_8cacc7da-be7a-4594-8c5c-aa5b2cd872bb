#!/usr/bin/env python3
"""
Volume Tools Assistant Key Generator
تحذير: للأغراض التعليمية فقط
"""

import random
import string
import hashlib

def generate_key():
    """توليد مفتاح ترخيص"""
    
    # خوارزمية بسيطة لتوليد المفاتيح
    chars = string.ascii_uppercase + string.digits
    
    # توليد أجزاء المفتاح
    part1 = ''.join(random.choices(chars, k=4))
    part2 = ''.join(random.choices(chars, k=4))
    part3 = ''.join(random.choices(chars, k=4))
    part4 = ''.join(random.choices(chars, k=4))
    
    key = f"{part1}-{part2}-{part3}-{part4}"
    
    return key

def generate_serial():
    """توليد رقم تسلسلي"""
    
    # توليد رقم تسلسلي بناءً على خوارزمية معينة
    base = "VTA76"
    random_part = ''.join(random.choices(string.digits, k=8))
    
    # حساب checksum بسيط
    checksum = sum(ord(c) for c in base + random_part) % 100
    
    serial = f"{base}-{random_part}-{checksum:02d}"
    
    return serial

if __name__ == "__main__":
    print("🔑 Volume Tools Assistant Key Generator")
    print("=" * 40)
    
    for i in range(5):
        key = generate_key()
        serial = generate_serial()
        
        print(f"Key {i+1}: {key}")
        print(f"Serial {i+1}: {serial}")
        print("-" * 30)
