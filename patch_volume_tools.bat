@echo off
chcp 65001 >nul
title Volume Tools Assistant Patcher

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Volume Tools Assistant Patcher                ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  تحذير: هذا البرنامج للأغراض التعليمية فقط!                  ║
echo ║  يُنصح بشراء الترخيص الأصلي لدعم المطورين.                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً.
    pause
    exit /b 1
)

:: التحقق من وجود الملف الأصلي
if not exist "Volume Tools Assistant 7.6.exe" (
    echo ❌ الملف الأصلي غير موجود!
    echo يرجى وضع الملف في نفس المجلد.
    pause
    exit /b 1
)

echo 🔍 تم العثور على الملف الأصلي...
echo.

:: تشغيل الـ patcher
echo 🛠️  تشغيل الـ patcher...
python volume_tools_patch.py

echo.
echo ✅ انتهت العملية!
echo.

:: عرض الملفات المُنشأة
echo 📁 الملفات المُنشأة:
if exist "Volume Tools Assistant 7.6_patched.exe" (
    echo    ✓ Volume Tools Assistant 7.6_patched.exe
)
if exist "volume_tools_keygen.py" (
    echo    ✓ volume_tools_keygen.py
)

echo.
echo 📝 تعليمات الاستخدام:
echo    1. استخدم الملف المعدل بدلاً من الأصلي
echo    2. إذا طُلب منك مفتاح، استخدم الـ keygen
echo    3. احتفظ بنسخة احتياطية من الملف الأصلي
echo.

pause
